import { devtools } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';

import { fetchApi } from '@/libs/utils/api';
import { GetDataWithPagination } from '@/types/utility';
import { buildQueryString, createStore } from '@/utils';
import { useClinicStore } from '@/apps/shop/stores/useClinicStore';
import { queryClient, queryKeys } from '@/libs/query/queryClient';

import { Actions, SearchState, State } from './types';
import { ProductType, SearchParamsProps } from '@/types';
import { PromoType } from '@/types/common';

const SEARCH_INITIAL_STATE: SearchState = {
  productList: [],
  page: 1,
  total: 0,
  perPage: '12',
  query: '',
  sortOptions: {
    sortOrder: undefined,
    sortBy: undefined,
  },
  filterOptions: {
    vendorIds: '',
  },
};

export const useProductStore = createStore<State & Actions>()(
  immer(
    devtools((set, getState) => ({
      ...SEARCH_INITIAL_STATE,
      fetchProductDetails: async (id?: string) => {
        const clinicId = useClinicStore.getState().clinic?.id;

        const response = await fetchApi<ProductType>(
          `/clinics/${clinicId}/products/${id}`,
        );

        return response;
      },
      fetchProductPromotions: async (productId?: string) => {
        const clinicId = useClinicStore.getState().clinic?.id;

        const response = await fetchApi<PromoType[]>(
          `/clinics/${clinicId}/products/${productId}/promotions`,
        );

        return response;
      },
      updateSearchQueryValue: (query) => set({ query }),
      getSearchProduct: async (
        params: Partial<SearchParamsProps<ProductType>>,
        updateQuery,
      ) => {
        const { sortOptions, query } = getState();
        const clinicId = useClinicStore.getState().clinic?.id;

        const queryParams: SearchParamsProps<ProductType> = {
          query: query.trim(),
          sortBy: sortOptions.sortBy,
          sortOrder: sortOptions.sortOrder,
          clinicId: clinicId || '',
          page: params.page || getState().page || 1,
          perPage: params.perPage || getState().perPage || '12',
          vendorIds:
            params.vendorIds || getState().filterOptions.vendorIds || '',
          ...params,
        };

        if (!queryParams.query) {
          return;
        }

        const { sortBy, sortOrder, page, perPage, vendorIds, ...rest } =
          queryParams;
        let newQuery =
          buildQueryString<Partial<SearchParamsProps<ProductType>>>(rest);

        if (page) newQuery += `&page[number]=${page}`;
        if (perPage) newQuery += `&page[size]=${perPage}`;
        if (vendorIds) newQuery += `&filter[vendorIds]=${vendorIds}`;

        const response = {
          data: [
            {
              id: '9ed26eac-7240-4020-9366-fe04290b77c3',
              name: 'Simparica 6 x 5mg Gold 2.8 - 5.5lbs',
              imageUrl:
                'https://shop.zoetis.com/zb2b/medias/300Wx300H-null?context=bWFzdGVyfHJvb3R8MjQ3NDN8aW1hZ2UvanBlZ3xhREZrTDJneVlpOHhNalUxTmpRMk1EWTROek01TUM4ek1EQlhlRE13TUVoZmJuVnNiQXwyNjJkZDg5OWQ1ZDZhN2NmMTUzMzU0NzNmNDEzZTQ5ODk0MjFhYjYyZmY4ZWUyYzA2NTIwY2JlNTQ2MTc1YzU1',
              isFavorite: false,
              manufacturer: null,
              manufacturerSku: '10012457',
              description:
                'Persistent Protection from ticks and fleas.nSimparicau00aeu00a0(sarolaner)u00a0is FDA-approved to block infections that may cause Lyme disease as a result of killingu00a0ixodes scapularis.',
              offers: [
                {
                  id: '9d7581c7-ebe0-468e-8b8a-6221b3476f70',
                  vendor: {
                    id: '9d7559b3-2608-4f66-a75a-1f1b77cd472d',
                    name: 'Zoetis',
                    imageUrl:
                      'https://staging.services.highfive.vet/storage/vendor-images/zoetis.png',
                    type: 'manufacturer',
                  },
                  isPurchasable: true,
                  vendorSku: '10012457',
                  price: '75.35',
                  clinicPrice: null,
                  stockStatus: 'IN_STOCK',
                  lastOrderedAt: '2025-09-09T16:43:13.000000Z',
                  lastOrderedQuantity: 2,
                  increments: 1,
                  isRecommended: true,
                  rebatePercent: '10.0',
                  unitOfMeasure: null,
                  size: null,
                  rawCategory1: null,
                  rawCategory2: null,
                  rawCategory3: null,
                  rawCategory4: null,
                },
              ],
              isHazardous: false,
              requiresPrescription: false,
              requiresColdShipping: false,
              isControlledSubstance: false,
              isControlled222Form: false,
              requiresPedigree: false,
              nationalDrugCode: null,
            },
            {
              id: '9ed26eac-7aec-4b5d-8776-eca132ff3884',
              name: 'Simparica 6 x 40mg Teal 22.1 - 44lbs',
              imageUrl:
                'https://shop.zoetis.com/zb2b/medias/300Wx300H-null?context=bWFzdGVyfHJvb3R8MjQ1NzB8aW1hZ2UvanBlZ3xhRE01TDJobVpTOHhNalUxTmpRMk1qYzFNVGMzTkM4ek1EQlhlRE13TUVoZmJuVnNiQXxjYzgxZTc1NTM3MTk2YWZkZWEyMzM4OTM1ODFjN2UzMDIxZGE1ZTMzMmFiNzk3ZGU2ODcxNDA4MmMyZWIyMTlk',
              isFavorite: false,
              manufacturer: null,
              manufacturerSku: '10012460',
              description:
                'Persistent Protection from ticks and fleas.nSimparicau00aeu00a0(sarolaner)u00a0is FDA-approved to block infections that may cause Lyme disease as a result of killingu00a0ixodes scapularis.',
              offers: [
                {
                  id: '9d7581c7-ee74-417e-8465-ad8b2b275323',
                  vendor: {
                    id: '9d7559b3-2608-4f66-a75a-1f1b77cd472d',
                    name: 'Zoetis',
                    imageUrl:
                      'https://staging.services.highfive.vet/storage/vendor-images/zoetis.png',
                    type: 'manufacturer',
                  },
                  isPurchasable: true,
                  vendorSku: '10012460',
                  price: '84.40',
                  clinicPrice: null,
                  stockStatus: 'IN_STOCK',
                  lastOrderedAt: '2025-09-09T16:51:47.000000Z',
                  lastOrderedQuantity: 2,
                  increments: 1,
                  isRecommended: true,
                  rebatePercent: '10.0',
                  unitOfMeasure: null,
                  size: null,
                  rawCategory1: null,
                  rawCategory2: null,
                  rawCategory3: null,
                  rawCategory4: null,
                },
              ],
              isHazardous: false,
              requiresPrescription: false,
              requiresColdShipping: false,
              isControlledSubstance: false,
              isControlled222Form: false,
              requiresPedigree: false,
              nationalDrugCode: null,
            },
            {
              id: '9ed26eac-adb7-4be4-9e8a-bc64a120ec63',
              name: 'Simparica 6 x 80mg Green 44.1 - 88lbs',
              imageUrl:
                'https://shop.zoetis.com/zb2b/medias/300Wx300H-null?context=bWFzdGVyfHJvb3R8MjgzMTB8aW1hZ2UvanBlZ3xhR1k1TDJnNU5DOHhNalUxTmpRMk16Y3dNakEwTmk4ek1EQlhlRE13TUVoZmJuVnNiQXw0NjU5MzU4ODAxYWNiMWMwNzNjNDhiYmVhN2NiMTY2NjRlYWU0NGRiNTlkMzdhMjUyNDc1ZWVhNWEwMDVlYzAw',
              isFavorite: false,
              manufacturer: null,
              manufacturerSku: '10012461',
              description:
                'Persistent Protection from ticks and fleas.nSimparicau00aeu00a0(sarolaner)u00a0is FDA-approved to block infections that may cause Lyme disease as a result of killingu00a0ixodes scapularis.',
              offers: [
                {
                  id: '9d7581c8-30ab-4c26-912f-a7f82989f13c',
                  vendor: {
                    id: '9d7559b3-2608-4f66-a75a-1f1b77cd472d',
                    name: 'Zoetis',
                    imageUrl:
                      'https://staging.services.highfive.vet/storage/vendor-images/zoetis.png',
                    type: 'manufacturer',
                  },
                  isPurchasable: true,
                  vendorSku: '10012461',
                  price: '86.90',
                  clinicPrice: null,
                  stockStatus: 'IN_STOCK',
                  lastOrderedAt: null,
                  lastOrderedQuantity: null,
                  increments: 1,
                  isRecommended: true,
                  rebatePercent: '10.0',
                  unitOfMeasure: null,
                  size: null,
                  rawCategory1: null,
                  rawCategory2: null,
                  rawCategory3: null,
                  rawCategory4: null,
                },
              ],
              isHazardous: false,
              requiresPrescription: false,
              requiresColdShipping: false,
              isControlledSubstance: false,
              isControlled222Form: false,
              requiresPedigree: false,
              nationalDrugCode: null,
            },
            {
              id: '9ed26eac-2ec4-45d9-b3c9-d7d154c37ae7',
              name: 'Simparica 6 x 120mg Dark Brown 88.1 - 132lbs',
              imageUrl:
                'https://shop.zoetis.com/zb2b/medias/300Wx300H-null?context=bWFzdGVyfHJvb3R8MjM5OTh8aW1hZ2UvanBlZ3xhRFF5TDJnek9DOHhNalUxTmpRMk5ETTFOelF3Tmk4ek1EQlhlRE13TUVoZmJuVnNiQXw5NGM3YzBmZWE5MmQwYWZlM2RmMTY0YTg0MzJkN2Y4MmRjMjU4OTI4MTFlMzk0YWQ1MTIwYWZjOGZkMmI5OWJh',
              isFavorite: false,
              manufacturer: null,
              manufacturerSku: '10012462',
              description:
                'Persistent Protection from ticks and fleas.nSimparicau00aeu00a0(sarolaner)u00a0is FDA-approved to block infections that may cause Lyme disease as a result of killing ixodes scapularis.',
              offers: [
                {
                  id: '9d7581c7-c967-41fb-aef1-d991203afdcb',
                  vendor: {
                    id: '9d7559b3-2608-4f66-a75a-1f1b77cd472d',
                    name: 'Zoetis',
                    imageUrl:
                      'https://staging.services.highfive.vet/storage/vendor-images/zoetis.png',
                    type: 'manufacturer',
                  },
                  isPurchasable: true,
                  vendorSku: '10012462',
                  price: '88.00',
                  clinicPrice: null,
                  stockStatus: 'IN_STOCK',
                  lastOrderedAt: '2025-08-26T18:50:18.000000Z',
                  lastOrderedQuantity: 1,
                  increments: 1,
                  isRecommended: true,
                  rebatePercent: '10.0',
                  unitOfMeasure: null,
                  size: null,
                  rawCategory1: null,
                  rawCategory2: null,
                  rawCategory3: null,
                  rawCategory4: null,
                },
              ],
              isHazardous: false,
              requiresPrescription: false,
              requiresColdShipping: false,
              isControlledSubstance: false,
              isControlled222Form: false,
              requiresPedigree: false,
              nationalDrugCode: null,
            },
            {
              id: '9ed26eac-6dc5-418b-a451-f69a274881ce',
              name: 'Simparica 6 x 20mg Light Brown 11.1 - 22lbs',
              imageUrl:
                'https://shop.zoetis.com/zb2b/medias/300Wx300H-null?context=bWFzdGVyfHJvb3R8MjUyMzR8aW1hZ2UvanBlZ3xhRFUzTDJobFlTOHhNalUxTmpRMk1qQTVOalF4TkM4ek1EQlhlRE13TUVoZmJuVnNiQXw0NmI2NGFjMDBkMTI0MzEzZWM3YzhkN2NhMzE3ZWJkZDJiYTliZDkxNmRkYzBhZWM3MDJjMTkzZGZhY2Q3ZmFh',
              isFavorite: false,
              manufacturer: null,
              manufacturerSku: '10012459',
              description:
                'Persistent Protection from ticks and fleas.nSimparicau00aeu00a0(sarolaner)u00a0is FDA-approved to block infections that may cause Lyme disease as a result of killingu00a0ixodes scapularis.',
              offers: [
                {
                  id: '9d7581c7-ea9d-45a2-82c2-c44cf96f7978',
                  vendor: {
                    id: '9d7559b3-2608-4f66-a75a-1f1b77cd472d',
                    name: 'Zoetis',
                    imageUrl:
                      'https://staging.services.highfive.vet/storage/vendor-images/zoetis.png',
                    type: 'manufacturer',
                  },
                  isPurchasable: true,
                  vendorSku: '10012459',
                  price: '78.05',
                  clinicPrice: null,
                  stockStatus: 'IN_STOCK',
                  lastOrderedAt: '2025-08-26T18:50:18.000000Z',
                  lastOrderedQuantity: 3,
                  increments: 1,
                  isRecommended: true,
                  rebatePercent: '10.0',
                  unitOfMeasure: null,
                  size: null,
                  rawCategory1: null,
                  rawCategory2: null,
                  rawCategory3: null,
                  rawCategory4: null,
                },
              ],
              isHazardous: false,
              requiresPrescription: false,
              requiresColdShipping: false,
              isControlledSubstance: false,
              isControlled222Form: false,
              requiresPedigree: false,
              nationalDrugCode: null,
            },
            {
              id: '01969b4c-645d-73bd-9e1d-2d31da2c7728',
              name: 'Simparica Chewables for Dogs 2.8 to 5.5 Pounds, Gold Label (3 Dose x 10)',
              imageUrl:
                'https://ws.mwiah.com/media/image?id=9916dd3f-cc6c-4a81-836f-65bf913e7c69',
              isFavorite: true,
              manufacturer: 'Zoetis',
              manufacturerSku: '',
              description:
                'Simparica (sarolaner) kills adult fleas, and is indicated for the treatment and prevention of flea infestations (_Ctenocephalides felis_), and the treatment and control of tick infestations [_Amblyomma americanum_ (Lone Star tick), _Amblyomma maculatum_ (Gulf Coast tick), _Dermacentor variabilis_ (American dog tick), _Ixodes scapularis_ (black-legged tick), and _Rhipicephalus sanguineus_ (brown dog tick)] for one month in dogs 6 months of age or older and weighing 2.8 pounds or greater. See package insert for complete product details.',
              offers: [
                {
                  id: '9d7581c7-e947-40f0-adb7-471859462816',
                  vendor: {
                    id: '9d7559b3-2608-4f66-a75a-1f1b77cd472d',
                    name: 'Zoetis',
                    imageUrl:
                      'https://staging.services.highfive.vet/storage/vendor-images/zoetis.png',
                    type: 'manufacturer',
                  },
                  isPurchasable: true,
                  vendorSku: '10012449',
                  price: '41.55',
                  clinicPrice: null,
                  stockStatus: 'IN_STOCK',
                  lastOrderedAt: '2025-09-16T12:58:47.000000Z',
                  lastOrderedQuantity: 1,
                  increments: 1,
                  isRecommended: true,
                  rebatePercent: '10.0',
                  unitOfMeasure: null,
                  size: null,
                  rawCategory1: null,
                  rawCategory2: null,
                  rawCategory3: null,
                  rawCategory4: null,
                },
                {
                  id: '9d7ed145-68d3-4f21-ae0d-34829f7a1015',
                  vendor: {
                    id: '9d7559b3-0b71-4606-88b0-e903fc518846',
                    name: 'Covetrus',
                    imageUrl:
                      'https://staging.services.highfive.vet/storage/vendor-images/covetrus.png',
                    type: 'distributor',
                  },
                  isPurchasable: true,
                  vendorSku: '058769',
                  price: null,
                  clinicPrice: '415.50',
                  stockStatus: 'IN_STOCK',
                  lastOrderedAt: null,
                  lastOrderedQuantity: null,
                  increments: 1,
                  isRecommended: false,
                  rebatePercent: null,
                  unitOfMeasure: null,
                  size: null,
                  rawCategory1: null,
                  rawCategory2: null,
                  rawCategory3: null,
                  rawCategory4: null,
                },
                {
                  id: '9d7ed20d-f715-4086-9e89-06dcf412b061',
                  vendor: {
                    id: '9d7559b3-01d7-4c19-9836-48271ec9e9ad',
                    name: 'Patterson',
                    imageUrl:
                      'https://staging.services.highfive.vet/storage/vendor-images/patterson.png',
                    type: 'distributor',
                  },
                  isPurchasable: true,
                  vendorSku: '07-893-0551',
                  price: null,
                  clinicPrice: '415.50',
                  stockStatus: 'IN_STOCK',
                  lastOrderedAt: null,
                  lastOrderedQuantity: null,
                  increments: 1,
                  isRecommended: false,
                  rebatePercent: null,
                  unitOfMeasure: null,
                  size: null,
                  rawCategory1: null,
                  rawCategory2: null,
                  rawCategory3: null,
                  rawCategory4: null,
                },
              ],
              isHazardous: false,
              requiresPrescription: false,
              requiresColdShipping: false,
              isControlledSubstance: false,
              isControlled222Form: false,
              requiresPedigree: false,
              nationalDrugCode: null,
            },
            {
              id: '01969b4c-645d-73e1-a6db-b0f05930edf2',
              name: 'Simparica Chewables for Dogs 44.1 to 88 Pounds, Green Label (3 Dose x 10)',
              imageUrl:
                'https://ws.mwiah.com/media/image?id=d8cfc61b-3738-4cac-8602-aad7158b766a',
              isFavorite: false,
              manufacturer: 'Zoetis',
              manufacturerSku: '',
              description:
                'Simparica (sarolaner) kills adult fleas, and is indicated for the treatment and prevention of flea infestations (_Ctenocephalides felis_), and the treatment and control of tick infestations [_Amblyomma americanum_ (Lone Star tick), _Amblyomma maculatum_ (Gulf Coast tick), _Dermacentor variabilis_ (American dog tick), _Ixodes scapularis_ (black-legged tick), and _Rhipicephalus sanguineus_ (brown dog tick)] for one month in dogs 6 months of age or older and weighing 2.8 pounds or greater. See package insert for complete product details.',
              offers: [
                {
                  id: '9d7581c8-35da-4e08-85c1-89b4148ee967',
                  vendor: {
                    id: '9d7559b3-2608-4f66-a75a-1f1b77cd472d',
                    name: 'Zoetis',
                    imageUrl:
                      'https://staging.services.highfive.vet/storage/vendor-images/zoetis.png',
                    type: 'manufacturer',
                  },
                  isPurchasable: true,
                  vendorSku: '10012455',
                  price: '47.80',
                  clinicPrice: null,
                  stockStatus: 'IN_STOCK',
                  lastOrderedAt: null,
                  lastOrderedQuantity: null,
                  increments: 1,
                  isRecommended: true,
                  rebatePercent: '10.0',
                  unitOfMeasure: null,
                  size: null,
                  rawCategory1: null,
                  rawCategory2: null,
                  rawCategory3: null,
                  rawCategory4: null,
                },
                {
                  id: '9d7ed145-765f-40d8-b2c0-522811232ea3',
                  vendor: {
                    id: '9d7559b3-0b71-4606-88b0-e903fc518846',
                    name: 'Covetrus',
                    imageUrl:
                      'https://staging.services.highfive.vet/storage/vendor-images/covetrus.png',
                    type: 'distributor',
                  },
                  isPurchasable: true,
                  vendorSku: '058773',
                  price: null,
                  clinicPrice: '478.00',
                  stockStatus: 'IN_STOCK',
                  lastOrderedAt: null,
                  lastOrderedQuantity: null,
                  increments: 1,
                  isRecommended: false,
                  rebatePercent: null,
                  unitOfMeasure: null,
                  size: null,
                  rawCategory1: null,
                  rawCategory2: null,
                  rawCategory3: null,
                  rawCategory4: null,
                },
                {
                  id: '9d7ed20d-ff9f-4bc1-b890-fff5baef433e',
                  vendor: {
                    id: '9d7559b3-01d7-4c19-9836-48271ec9e9ad',
                    name: 'Patterson',
                    imageUrl:
                      'https://staging.services.highfive.vet/storage/vendor-images/patterson.png',
                    type: 'distributor',
                  },
                  isPurchasable: true,
                  vendorSku: '07-893-0553',
                  price: null,
                  clinicPrice: '478.00',
                  stockStatus: 'IN_STOCK',
                  lastOrderedAt: null,
                  lastOrderedQuantity: null,
                  increments: 1,
                  isRecommended: false,
                  rebatePercent: null,
                  unitOfMeasure: null,
                  size: null,
                  rawCategory1: null,
                  rawCategory2: null,
                  rawCategory3: null,
                  rawCategory4: null,
                },
              ],
              isHazardous: false,
              requiresPrescription: false,
              requiresColdShipping: false,
              isControlledSubstance: false,
              isControlled222Form: false,
              requiresPedigree: false,
              nationalDrugCode: null,
            },
            {
              id: '01969b4c-645d-745a-8ad3-48795a2dca07',
              name: 'Simparica Chewables for Dogs 11.1 to 22 Pounds, Orange Label (3 Dose x 10)',
              imageUrl:
                'https://ws.mwiah.com/media/image?id=4c98ce1f-1058-4f9a-8dc2-10855e4ec1ee',
              isFavorite: false,
              manufacturer: 'Zoetis',
              manufacturerSku: '',
              description:
                'Simparica (sarolaner) kills adult fleas, and is indicated for the treatment and prevention of flea infestations (_Ctenocephalides felis_), and the treatment and control of tick infestations [_Amblyomma americanum_ (Lone Star tick), _Amblyomma maculatum_ (Gulf Coast tick), _Dermacentor variabilis_ (American dog tick), _Ixodes scapularis_ (black-legged tick), and _Rhipicephalus sanguineus_ (brown dog tick)] for one month in dogs 6 months of age or older and weighing 2.8 pounds or greater. See package insert for complete product details.',
              offers: [
                {
                  id: '9d7581c7-ed27-4e03-8d2f-8174670e0f36',
                  vendor: {
                    id: '9d7559b3-2608-4f66-a75a-1f1b77cd472d',
                    name: 'Zoetis',
                    imageUrl:
                      'https://staging.services.highfive.vet/storage/vendor-images/zoetis.png',
                    type: 'manufacturer',
                  },
                  isPurchasable: true,
                  vendorSku: '10012452',
                  price: '43.05',
                  clinicPrice: null,
                  stockStatus: 'IN_STOCK',
                  lastOrderedAt: null,
                  lastOrderedQuantity: null,
                  increments: 1,
                  isRecommended: true,
                  rebatePercent: '10.0',
                  unitOfMeasure: null,
                  size: null,
                  rawCategory1: null,
                  rawCategory2: null,
                  rawCategory3: null,
                  rawCategory4: null,
                },
                {
                  id: '9d7ed145-5577-4f28-b3d9-63e3f6c82f92',
                  vendor: {
                    id: '9d7559b3-0b71-4606-88b0-e903fc518846',
                    name: 'Covetrus',
                    imageUrl:
                      'https://staging.services.highfive.vet/storage/vendor-images/covetrus.png',
                    type: 'distributor',
                  },
                  isPurchasable: true,
                  vendorSku: '058771',
                  price: null,
                  clinicPrice: '430.50',
                  stockStatus: 'IN_STOCK',
                  lastOrderedAt: null,
                  lastOrderedQuantity: null,
                  increments: 1,
                  isRecommended: false,
                  rebatePercent: null,
                  unitOfMeasure: null,
                  size: null,
                  rawCategory1: null,
                  rawCategory2: null,
                  rawCategory3: null,
                  rawCategory4: null,
                },
                {
                  id: '9d7ed20d-f026-4fe5-835b-967ab0752a8b',
                  vendor: {
                    id: '9d7559b3-01d7-4c19-9836-48271ec9e9ad',
                    name: 'Patterson',
                    imageUrl:
                      'https://staging.services.highfive.vet/storage/vendor-images/patterson.png',
                    type: 'distributor',
                  },
                  isPurchasable: true,
                  vendorSku: '07-893-0547',
                  price: null,
                  clinicPrice: '430.50',
                  stockStatus: 'IN_STOCK',
                  lastOrderedAt: null,
                  lastOrderedQuantity: null,
                  increments: 1,
                  isRecommended: false,
                  rebatePercent: null,
                  unitOfMeasure: null,
                  size: null,
                  rawCategory1: null,
                  rawCategory2: null,
                  rawCategory3: null,
                  rawCategory4: null,
                },
              ],
              isHazardous: false,
              requiresPrescription: false,
              requiresColdShipping: false,
              isControlledSubstance: false,
              isControlled222Form: false,
              requiresPedigree: false,
              nationalDrugCode: null,
            },
            {
              id: '01969b4c-645d-7836-b8f3-2d802dba476d',
              name: 'Simparica Chewables for Dogs 88.1 to 132 Pounds, Brown Label (3 Dose x 10)',
              imageUrl:
                'https://ws.mwiah.com/media/image?id=4b911a98-5007-4271-b556-ec2ae02b79ac',
              isFavorite: false,
              manufacturer: 'Zoetis',
              manufacturerSku: '',
              description:
                'Simparica (sarolaner) kills adult fleas, and is indicated for the treatment and prevention of flea infestations (_Ctenocephalides felis_), and the treatment and control of tick infestations [_Amblyomma americanum_ (Lone Star tick), _Amblyomma maculatum_ (Gulf Coast tick), _Dermacentor variabilis_ (American dog tick), _Ixodes scapularis_ (black-legged tick), and _Rhipicephalus sanguineus_ (brown dog tick)] for one month in dogs 6 months of age or older and weighing 2.8 pounds or greater. See package insert for complete product details.',
              offers: [
                {
                  id: '9d7581c8-33f2-4b1e-8816-c9ead46312a8',
                  vendor: {
                    id: '9d7559b3-2608-4f66-a75a-1f1b77cd472d',
                    name: 'Zoetis',
                    imageUrl:
                      'https://staging.services.highfive.vet/storage/vendor-images/zoetis.png',
                    type: 'manufacturer',
                  },
                  isPurchasable: true,
                  vendorSku: '10012456',
                  price: '48.70',
                  clinicPrice: null,
                  stockStatus: 'IN_STOCK',
                  lastOrderedAt: null,
                  lastOrderedQuantity: null,
                  increments: 1,
                  isRecommended: true,
                  rebatePercent: '10.0',
                  unitOfMeasure: null,
                  size: null,
                  rawCategory1: null,
                  rawCategory2: null,
                  rawCategory3: null,
                  rawCategory4: null,
                },
                {
                  id: '9d7ed145-4c9f-4ee2-966a-e5944699de0e',
                  vendor: {
                    id: '9d7559b3-0b71-4606-88b0-e903fc518846',
                    name: 'Covetrus',
                    imageUrl:
                      'https://staging.services.highfive.vet/storage/vendor-images/covetrus.png',
                    type: 'distributor',
                  },
                  isPurchasable: true,
                  vendorSku: '058774',
                  price: null,
                  clinicPrice: '487.00',
                  stockStatus: 'IN_STOCK',
                  lastOrderedAt: null,
                  lastOrderedQuantity: null,
                  increments: 1,
                  isRecommended: false,
                  rebatePercent: null,
                  unitOfMeasure: null,
                  size: null,
                  rawCategory1: null,
                  rawCategory2: null,
                  rawCategory3: null,
                  rawCategory4: null,
                },
                {
                  id: '9d7ed20d-e938-4b10-87bf-d5aad697d702',
                  vendor: {
                    id: '9d7559b3-01d7-4c19-9836-48271ec9e9ad',
                    name: 'Patterson',
                    imageUrl:
                      'https://staging.services.highfive.vet/storage/vendor-images/patterson.png',
                    type: 'distributor',
                  },
                  isPurchasable: true,
                  vendorSku: '07-893-0545',
                  price: null,
                  clinicPrice: '487.00',
                  stockStatus: 'IN_STOCK',
                  lastOrderedAt: null,
                  lastOrderedQuantity: null,
                  increments: 1,
                  isRecommended: false,
                  rebatePercent: null,
                  unitOfMeasure: null,
                  size: null,
                  rawCategory1: null,
                  rawCategory2: null,
                  rawCategory3: null,
                  rawCategory4: null,
                },
              ],
              isHazardous: false,
              requiresPrescription: false,
              requiresColdShipping: false,
              isControlledSubstance: false,
              isControlled222Form: false,
              requiresPedigree: false,
              nationalDrugCode: null,
            },
            {
              id: '01969b4c-645d-78ac-9a40-74344b79148a',
              name: 'Simparica Chewables for Dogs 22.1 to 44 Pounds, Blue Label (3 Dose x 10)',
              imageUrl:
                'https://ws.mwiah.com/media/image?id=ca70125d-1051-4652-b26a-d2f8bbdf1b87',
              isFavorite: false,
              manufacturer: 'Zoetis',
              manufacturerSku: '',
              description:
                'Simparica (sarolaner) kills adult fleas, and is indicated for the treatment and prevention of flea infestations (_Ctenocephalides felis_), and the treatment and control of tick infestations [_Amblyomma americanum_ (Lone Star tick), _Amblyomma maculatum_ (Gulf Coast tick), _Dermacentor variabilis_ (American dog tick), _Ixodes scapularis_ (black-legged tick), and _Rhipicephalus sanguineus_ (brown dog tick)] for one month in dogs 6 months of age or older and weighing 2.8 pounds or greater. See package insert for complete product details.',
              offers: [
                {
                  id: '9d7581c7-ce0f-4e3d-b589-9451c4ac0aa0',
                  vendor: {
                    id: '9d7559b3-2608-4f66-a75a-1f1b77cd472d',
                    name: 'Zoetis',
                    imageUrl:
                      'https://staging.services.highfive.vet/storage/vendor-images/zoetis.png',
                    type: 'manufacturer',
                  },
                  isPurchasable: true,
                  vendorSku: '10012454',
                  price: '46.25',
                  clinicPrice: null,
                  stockStatus: 'IN_STOCK',
                  lastOrderedAt: null,
                  lastOrderedQuantity: null,
                  increments: 1,
                  isRecommended: true,
                  rebatePercent: '10.0',
                  unitOfMeasure: null,
                  size: null,
                  rawCategory1: null,
                  rawCategory2: null,
                  rawCategory3: null,
                  rawCategory4: null,
                },
                {
                  id: '9d7ed145-5ee1-408a-8ea6-3f1064e9484c',
                  vendor: {
                    id: '9d7559b3-0b71-4606-88b0-e903fc518846',
                    name: 'Covetrus',
                    imageUrl:
                      'https://staging.services.highfive.vet/storage/vendor-images/covetrus.png',
                    type: 'distributor',
                  },
                  isPurchasable: true,
                  vendorSku: '058772',
                  price: null,
                  clinicPrice: '462.50',
                  stockStatus: 'IN_STOCK',
                  lastOrderedAt: null,
                  lastOrderedQuantity: null,
                  increments: 1,
                  isRecommended: false,
                  rebatePercent: null,
                  unitOfMeasure: null,
                  size: null,
                  rawCategory1: null,
                  rawCategory2: null,
                  rawCategory3: null,
                  rawCategory4: null,
                },
                {
                  id: '9d7ed20d-e1cb-44a6-856c-d1687b65df82',
                  vendor: {
                    id: '9d7559b3-01d7-4c19-9836-48271ec9e9ad',
                    name: 'Patterson',
                    imageUrl:
                      'https://staging.services.highfive.vet/storage/vendor-images/patterson.png',
                    type: 'distributor',
                  },
                  isPurchasable: true,
                  vendorSku: '07-893-0549',
                  price: null,
                  clinicPrice: '462.50',
                  stockStatus: 'IN_STOCK',
                  lastOrderedAt: null,
                  lastOrderedQuantity: null,
                  increments: 1,
                  isRecommended: false,
                  rebatePercent: null,
                  unitOfMeasure: null,
                  size: null,
                  rawCategory1: null,
                  rawCategory2: null,
                  rawCategory3: null,
                  rawCategory4: null,
                },
              ],
              isHazardous: false,
              requiresPrescription: false,
              requiresColdShipping: false,
              isControlledSubstance: false,
              isControlled222Form: false,
              requiresPedigree: false,
              nationalDrugCode: null,
            },
            {
              id: '01969b4c-645d-78e1-b58b-0a599395f20e',
              name: 'Simparica Chewables for Dogs 5.6 to 11 Pounds, Purple Label (3 Dose x 10)',
              imageUrl:
                'https://ws.mwiah.com/media/image?id=111e2a0a-2834-4682-a08d-82b43fe77309',
              isFavorite: false,
              manufacturer: 'Zoetis',
              manufacturerSku: '',
              description:
                'Simparica (sarolaner) kills adult fleas, and is indicated for the treatment and prevention of flea infestations (_Ctenocephalides felis_), and the treatment and control of tick infestations [_Amblyomma americanum_ (Lone Star tick), _Amblyomma maculatum_ (Gulf Coast tick), _Dermacentor variabilis_ (American dog tick), _Ixodes scapularis_ (black-legged tick), and _Rhipicephalus sanguineus_ (brown dog tick)] for one month in dogs 6 months of age or older and weighing 2.8 pounds or greater. See package insert for complete product details.',
              offers: [
                {
                  id: '9d7581c8-39a6-4a19-8cdd-1ecd9a5a111c',
                  vendor: {
                    id: '9d7559b3-2608-4f66-a75a-1f1b77cd472d',
                    name: 'Zoetis',
                    imageUrl:
                      'https://staging.services.highfive.vet/storage/vendor-images/zoetis.png',
                    type: 'manufacturer',
                  },
                  isPurchasable: true,
                  vendorSku: '10012450',
                  price: '42.35',
                  clinicPrice: null,
                  stockStatus: 'IN_STOCK',
                  lastOrderedAt: null,
                  lastOrderedQuantity: null,
                  increments: 1,
                  isRecommended: true,
                  rebatePercent: '10.0',
                  unitOfMeasure: null,
                  size: null,
                  rawCategory1: null,
                  rawCategory2: null,
                  rawCategory3: null,
                  rawCategory4: null,
                },
                {
                  id: '9d7ed145-4493-4767-8d95-840355074031',
                  vendor: {
                    id: '9d7559b3-0b71-4606-88b0-e903fc518846',
                    name: 'Covetrus',
                    imageUrl:
                      'https://staging.services.highfive.vet/storage/vendor-images/covetrus.png',
                    type: 'distributor',
                  },
                  isPurchasable: true,
                  vendorSku: '058770',
                  price: null,
                  clinicPrice: '423.50',
                  stockStatus: 'IN_STOCK',
                  lastOrderedAt: null,
                  lastOrderedQuantity: null,
                  increments: 1,
                  isRecommended: false,
                  rebatePercent: null,
                  unitOfMeasure: null,
                  size: null,
                  rawCategory1: null,
                  rawCategory2: null,
                  rawCategory3: null,
                  rawCategory4: null,
                },
                {
                  id: '9d7ed20e-07f0-467f-8e0c-9861056fe6d2',
                  vendor: {
                    id: '9d7559b3-01d7-4c19-9836-48271ec9e9ad',
                    name: 'Patterson',
                    imageUrl:
                      'https://staging.services.highfive.vet/storage/vendor-images/patterson.png',
                    type: 'distributor',
                  },
                  isPurchasable: true,
                  vendorSku: '07-893-0543',
                  price: null,
                  clinicPrice: '423.50',
                  stockStatus: 'IN_STOCK',
                  lastOrderedAt: null,
                  lastOrderedQuantity: null,
                  increments: 1,
                  isRecommended: false,
                  rebatePercent: null,
                  unitOfMeasure: null,
                  size: null,
                  rawCategory1: null,
                  rawCategory2: null,
                  rawCategory3: null,
                  rawCategory4: null,
                },
              ],
              isHazardous: false,
              requiresPrescription: false,
              requiresColdShipping: false,
              isControlledSubstance: false,
              isControlled222Form: false,
              requiresPedigree: false,
              nationalDrugCode: null,
            },
            {
              id: '01969b4c-645d-702f-b578-fb384e7a063f',
              name: 'Simparica Trio Chewable Tablets for Dogs 44.1 to 88 Pounds, Green Label (6 Dose x 5)',
              imageUrl:
                'https://ws.mwiah.com/media/image?id=b2bcfeb9-4901-4324-af97-61f70bd91c11',
              isFavorite: false,
              manufacturer: 'Zoetis',
              manufacturerSku: '',
              description:
                'Simparica TRIO (sarolaner, moxidectin, and pyrantel) Chewable Tablets prevent heartworm disease caused by _Dirofilaria immitis_, kills adult fleas (_Ctenocephalides felis_) and is indicated for the treatment and prevention of flea infestations, the treatment and control of tick infestations with _Amblyomma americanum_ (lone star tick), _Amblyomma maculatum_ (Gulf Coast tick), _Dermacentor variabilis_ (American dog tick), _Ixodes scapularis_ (black-legged tick), and _Rhipicephalus sanguineus_ (brown dog tick), and the treatment and control of roundworm (immature adult and adult _Toxocara_ _canis_ and adult _Toxascaris leonina_) and adult hookworm (_Ancylostoma_ _caninum_ and _Uncinaria stenocephala_) infections for one month in dogs and puppies 8 weeks of age and older, and weighing 2.8 pounds or greater. Simparica TRIO is indicated for the prevention of _Borrelia burgdorferi_ infections as a direct result of killing _Ixodes scapularis_ vector ticks. See package insert for complete product details.',
              offers: [
                {
                  id: '9d757225-e426-42e1-98e0-aa91185a4318',
                  vendor: {
                    id: '9d7559b3-2608-4f66-a75a-1f1b77cd472d',
                    name: 'Zoetis',
                    imageUrl:
                      'https://staging.services.highfive.vet/storage/vendor-images/zoetis.png',
                    type: 'manufacturer',
                  },
                  isPurchasable: true,
                  vendorSku: '10022682',
                  price: '135.60',
                  clinicPrice: null,
                  stockStatus: 'IN_STOCK',
                  lastOrderedAt: '2025-09-09T12:44:53.000000Z',
                  lastOrderedQuantity: 10,
                  increments: 10,
                  isRecommended: true,
                  rebatePercent: '8.0',
                  unitOfMeasure: null,
                  size: null,
                  rawCategory1: null,
                  rawCategory2: null,
                  rawCategory3: null,
                  rawCategory4: null,
                },
                {
                  id: '9d7ed145-8a6f-475c-aa51-69c27df1fadc',
                  vendor: {
                    id: '9d7559b3-0b71-4606-88b0-e903fc518846',
                    name: 'Covetrus',
                    imageUrl:
                      'https://staging.services.highfive.vet/storage/vendor-images/covetrus.png',
                    type: 'distributor',
                  },
                  isPurchasable: true,
                  vendorSku: '071607',
                  price: null,
                  clinicPrice: '678.00',
                  stockStatus: 'IN_STOCK',
                  lastOrderedAt: '2025-09-12T14:48:16.000000Z',
                  lastOrderedQuantity: 1,
                  increments: 1,
                  isRecommended: false,
                  rebatePercent: null,
                  unitOfMeasure: null,
                  size: null,
                  rawCategory1: null,
                  rawCategory2: null,
                  rawCategory3: null,
                  rawCategory4: null,
                },
                {
                  id: '9d7ed20d-dc5b-4ae5-9525-5a86c2a6786d',
                  vendor: {
                    id: '9d7559b3-01d7-4c19-9836-48271ec9e9ad',
                    name: 'Patterson',
                    imageUrl:
                      'https://staging.services.highfive.vet/storage/vendor-images/patterson.png',
                    type: 'distributor',
                  },
                  isPurchasable: true,
                  vendorSku: '07-894-5514',
                  price: null,
                  clinicPrice: '678.00',
                  stockStatus: 'IN_STOCK',
                  lastOrderedAt: null,
                  lastOrderedQuantity: null,
                  increments: 2,
                  isRecommended: false,
                  rebatePercent: null,
                  unitOfMeasure: null,
                  size: null,
                  rawCategory1: null,
                  rawCategory2: null,
                  rawCategory3: null,
                  rawCategory4: null,
                },
              ],
              isHazardous: false,
              requiresPrescription: false,
              requiresColdShipping: false,
              isControlledSubstance: false,
              isControlled222Form: false,
              requiresPedigree: false,
              nationalDrugCode: null,
            },
          ],
          links: {
            first:
              'https://staging.services.highfive.vet/api/clinics/9d9fa05e-02e7-4432-bdc5-0fd6aacde4d3/search?query=simparica&clinic_id=9d9fa05e-02e7-4432-bdc5-0fd6aacde4d3&page%5Bsize%5D=12&clinicId=9d9fa05e-02e7-4432-bdc5-0fd6aacde4d3&page%5Bnumber%5D=1',
            last: 'https://staging.services.highfive.vet/api/clinics/9d9fa05e-02e7-4432-bdc5-0fd6aacde4d3/search?query=simparica&clinic_id=9d9fa05e-02e7-4432-bdc5-0fd6aacde4d3&page%5Bsize%5D=12&clinicId=9d9fa05e-02e7-4432-bdc5-0fd6aacde4d3&page%5Bnumber%5D=3',
            prev: null,
            next: 'https://staging.services.highfive.vet/api/clinics/9d9fa05e-02e7-4432-bdc5-0fd6aacde4d3/search?query=simparica&clinic_id=9d9fa05e-02e7-4432-bdc5-0fd6aacde4d3&page%5Bsize%5D=12&clinicId=9d9fa05e-02e7-4432-bdc5-0fd6aacde4d3&page%5Bnumber%5D=2',
          },
          meta: {
            currentPage: 1,
            from: 1,
            lastPage: 3,
            links: [
              {
                url: null,
                label: '&laquo; Previous',
                page: null,
                active: false,
              },
              {
                url: 'https://staging.services.highfive.vet/api/clinics/9d9fa05e-02e7-4432-bdc5-0fd6aacde4d3/search?query=simparica&clinic_id=9d9fa05e-02e7-4432-bdc5-0fd6aacde4d3&page%5Bsize%5D=12&clinicId=9d9fa05e-02e7-4432-bdc5-0fd6aacde4d3&page%5Bnumber%5D=1',
                label: '1',
                page: 1,
                active: true,
              },
              {
                url: 'https://staging.services.highfive.vet/api/clinics/9d9fa05e-02e7-4432-bdc5-0fd6aacde4d3/search?query=simparica&clinic_id=9d9fa05e-02e7-4432-bdc5-0fd6aacde4d3&page%5Bsize%5D=12&clinicId=9d9fa05e-02e7-4432-bdc5-0fd6aacde4d3&page%5Bnumber%5D=2',
                label: '2',
                page: 2,
                active: false,
              },
              {
                url: 'https://staging.services.highfive.vet/api/clinics/9d9fa05e-02e7-4432-bdc5-0fd6aacde4d3/search?query=simparica&clinic_id=9d9fa05e-02e7-4432-bdc5-0fd6aacde4d3&page%5Bsize%5D=12&clinicId=9d9fa05e-02e7-4432-bdc5-0fd6aacde4d3&page%5Bnumber%5D=3',
                label: '3',
                page: 3,
                active: false,
              },
              {
                url: 'https://staging.services.highfive.vet/api/clinics/9d9fa05e-02e7-4432-bdc5-0fd6aacde4d3/search?query=simparica&clinic_id=9d9fa05e-02e7-4432-bdc5-0fd6aacde4d3&page%5Bsize%5D=12&clinicId=9d9fa05e-02e7-4432-bdc5-0fd6aacde4d3&page%5Bnumber%5D=2',
                label: 'Next &raquo;',
                page: 2,
                active: false,
              },
            ],
            path: 'https://staging.services.highfive.vet/api/clinics/9d9fa05e-02e7-4432-bdc5-0fd6aacde4d3/search',
            perPage: 12,
            to: 12,
            total: 34,
          },
        };

        // TBD the implementation of sorting by vendor
        // Bubble up first vendor filter to the top of the offers list
        // if (vendorIds?.length) {
        //   response.data.forEach((item) => {
        //     item.offers.sort((offer) =>
        //       offer.vendor.id === vendorIds![0] ? -1 : 1,
        //     );
        //   });
        // }

        set({
          productList: response.data,
          total: response.meta.total,
          sortOptions: {
            sortBy,
            sortOrder,
          },
          filterOptions: {
            vendorIds: vendorIds ?? '',
          },
          page,
          perPage,
          ...rest,
        });

        updateQuery(
          buildQueryString<SearchParamsProps<ProductType>>(queryParams),
        );
      },
      clearSearchProduct: () => set({ ...SEARCH_INITIAL_STATE }),
      addToFavorite: async (productId) => {
        const clinicId = useClinicStore.getState().clinic?.id;
        try {
          await fetchApi<ProductType>(
            `/clinics/${clinicId}/favorite-products`,
            {
              method: 'POST',
              body: {
                productId,
              },
            },
          );

          return true;
        } catch {
          return false;
        }
      },
      removeToFavorite: async (productId) => {
        const clinicId = useClinicStore.getState().clinic?.id;

        try {
          await fetchApi(
            `/clinics/${clinicId}/favorite-products/${productId}`,
            {
              method: 'DELETE',
            },
          );

          return true;
        } catch {
          return false;
        }
      },
      updateProductList: (list) => {
        set({ productList: list });
      },
    })),
  ),
);
