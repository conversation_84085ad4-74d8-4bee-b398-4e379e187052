import { useState } from 'react';
import { Tabs } from '@/libs/ui/Tabs/Tabs';
import { SuggestedOfferItem } from '@/libs/products/components/SuggestedOfferList/SuggestedOfferItem';
import { VendorSwap } from '@/libs/products/components/VendorSwap/VendorSwap';

const product = {
  id: '01969b4c-645d-78e1-b58b-0a599395f20e',
  name: 'Simparica Chewables for Dogs 5.6 to 11 Pounds, Purple Label (3 Dose x 10)',
  imageUrl:
    'https://ws.mwiah.com/media/image?id=111e2a0a-2834-4682-a08d-82b43fe77309',
  isFavorite: false,
  manufacturer: 'Zoe<PERSON>',
  manufacturerSku: '',
  description:
    '<PERSON><PERSON><PERSON><PERSON> (sarolaner) kills adult fleas, and is indicated for the treatment and prevention of flea infestations (_Ctenocephalides felis_), and the treatment and control of tick infestations \\[_Amblyomma americanum_ (Lone Star tick), _Amblyomma maculatum_ (Gulf Coast tick), _Dermacentor variabilis_ (American dog tick), _Ixodes scapularis_ (black-legged tick), and _Rhipicephalus sanguineus_ (brown dog tick)\\] for one month in dogs 6 months of age or older and weighing 2.8 pounds or greater. See package insert for complete product details.',
  offers: [
    {
      id: '9d7581c8-39a6-4a19-8cdd-1ecd9a5a111c',
      vendor: {
        id: '9d7559b3-2608-4f66-a75a-1f1b77cd472d',
        name: 'Zoetis',
        imageUrl:
          'https://staging.services.highfive.vet/storage/vendor-images/zoetis.png',
        type: 'manufacturer',
      },
      isPurchasable: true,
      vendorSku: '10012450',
      price: '42.35',
      clinicPrice: null,
      stockStatus: 'IN_STOCK',
      lastOrderedAt: null,
      lastOrderedQuantity: null,
      increments: 1,
      isRecommended: true,
      rebatePercent: '10.0',
      unitOfMeasure: null,
      size: null,
      rawCategory1: null,
      rawCategory2: null,
      rawCategory3: null,
      rawCategory4: null,
    },
    {
      id: '9d7ed145-4493-4767-8d95-840355074031',
      vendor: {
        id: '9d7559b3-0b71-4606-88b0-e903fc518846',
        name: 'Covetrus',
        imageUrl:
          'https://staging.services.highfive.vet/storage/vendor-images/covetrus.png',
        type: 'distributor',
      },
      isPurchasable: true,
      vendorSku: '058770',
      price: null,
      clinicPrice: '423.50',
      stockStatus: 'IN_STOCK',
      lastOrderedAt: null,
      lastOrderedQuantity: null,
      increments: 1,
      isRecommended: false,
      rebatePercent: null,
      unitOfMeasure: null,
      size: null,
      rawCategory1: null,
      rawCategory2: null,
      rawCategory3: null,
      rawCategory4: null,
    },
    {
      id: '9d7ed20e-07f0-467f-8e0c-9861056fe6d2',
      vendor: {
        id: '9d7559b3-01d7-4c19-9836-48271ec9e9ad',
        name: 'Patterson',
        imageUrl:
          'https://staging.services.highfive.vet/storage/vendor-images/patterson.png',
        type: 'distributor',
      },
      isPurchasable: true,
      vendorSku: '07-893-0543',
      price: null,
      clinicPrice: '423.50',
      stockStatus: 'IN_STOCK',
      lastOrderedAt: null,
      lastOrderedQuantity: null,
      increments: 1,
      isRecommended: false,
      rebatePercent: null,
      unitOfMeasure: null,
      size: null,
      rawCategory1: null,
      rawCategory2: null,
      rawCategory3: null,
      rawCategory4: null,
    },
  ],
  isHazardous: false,
  requiresPrescription: false,
  requiresColdShipping: false,
  isControlledSubstance: false,
  isControlled222Form: false,
  requiresPedigree: false,
  nationalDrugCode: null,
};

export const ShoppingList = () => {
  const [activeTab, setActiveTab] = useState(0);
  const [currentOffer, setCurrentOffer] = useState(product.offers[0]);

  const tabs = [
    {
      label: 'Your Custom Lists',
      onClick: (index: number) => setActiveTab(index),
    },
    {
      label: 'Previously Purchased',
      onClick: (index: number) => setActiveTab(index),
    },
  ];

  const handleSwapVendor = (newOfferId: string) => {
    setCurrentOffer(product.offers.find(({ id }) => newOfferId === id)!);
  };

  return (
    <div className="m-6 mt-8 flex h-full flex-col items-center rounded-sm border border-black/[0.06] bg-white p-6">
      <h1 className="text-lg font-semibold">Your Shopping Lists</h1>
      <div className="mt-5 w-lg font-medium">
        <Tabs active={activeTab} tabs={tabs} />
      </div>
      <div className="mt-6 w-full rounded-sm bg-gray-100/50 p-4">
        {activeTab === 0 && (
          <div className="h-24 w-full rounded-lg border border-black/[0.06] bg-white p-4">
            <div className="flex h-full items-center rounded-sm border border-cyan-100/25 bg-cyan-200 bg-gradient-to-t from-white/80 to-white/80">
              <VendorSwap
                currentOfferId={product.offers[0].id}
                offers={product.offers}
                onSwap={(newOfferId) => {
                  handleSwapVendor(newOfferId);
                }}
              />
            </div>
          </div>
        )}
        {activeTab === 1 && (
          <div className="text-center text-gray-600">
            Previously Purchased items
          </div>
        )}
      </div>
    </div>
  );
};
