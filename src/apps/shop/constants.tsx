import i18n from '@/apps/shop/i18n';
import { SHOP_ROUTES_PATH } from '@/apps/shop/routes/routes';

import HomeIcon from './assets/home.svg?react';
import CartIcon from './assets/cart.svg?react';
import ListIcon from './assets/list.svg?react';
import VendorsIcon from './assets/vendors.svg?react';
import SettingsIcon from './assets/settings.svg?react';
import { Icon } from '@/libs/icons/Icon';

export const CLINIC_NAV_LINKS = [
  {
    label: i18n.t('sidebar.home'),
    icon: <HomeIcon />,
    path: SHOP_ROUTES_PATH.clinicDashboard,
  },
  {
    label: i18n.t('sidebar.cart'),
    icon: <CartIcon />,
    path: SHOP_ROUTES_PATH.cart,
  },
  {
    label: i18n.t('sidebar.orderHistory'),
    icon: <ListIcon />,
    path: SHOP_ROUTES_PATH.orderHistory,
  },
  {
    label: i18n.t('sidebar.vendors'),
    icon: <VendorsIcon />,
    path: SHOP_ROUTES_PATH.vendors,
  },
  {
    label: i18n.t('sidebar.shoppingList'),
    icon: <Icon name="basket" size="1.5rem" />,
    path: SHOP_ROUTES_PATH.shoppingList,
  },
  {
    label: i18n.t('sidebar.invoices'),
    icon: <ListIcon />,
    path: SHOP_ROUTES_PATH.invoices,
  },
];

export const CLINIC_SETTING_LINKS = [
  {
    label: i18n.t('sidebar.settings'),
    icon: <SettingsIcon />,
    path: `${SHOP_ROUTES_PATH.settings}`,
  },
];

export const ACCOUNT_NAV_LINKS = [
  {
    label: i18n.t('sidebar.dashboard'),
    icon: <HomeIcon />,
    path: SHOP_ROUTES_PATH.accountDashboard,
  },
  {
    label: i18n.t('sidebar.clinicManagement'),
    icon: <ListIcon />,
    path: SHOP_ROUTES_PATH.clinicManagement,
  },
];

export const ACCOUNT_SETTING_LINKS = [
  {
    label: i18n.t('sidebar.accountSettings'),
    icon: <SettingsIcon />,
    path: SHOP_ROUTES_PATH.accountDetails,
  },
];
