import * as DropdownMenu from '@radix-ui/react-dropdown-menu';
import { Flex } from '@/libs/ui/Flex/Flex';
import { useMemo } from 'react';
import ArrowDown from './assets/arrow-down.svg?react';
import { ProductType } from '@/types';
import { getPriceString } from '@/utils';
import { StockStatusIcon } from '../StockStatusIcon/StockStatusIcon';
import { mergeClasses } from '@/utils';

type VendorSwapProps = {
  currentOfferId: string;
  offers: ProductType['offers'];
  onSwap: (productOfferId: string) => void;
};

export const VendorSwap = ({
  offers,
  currentOfferId,
  onSwap,
}: VendorSwapProps) => {
  const handleSelectSwapVendor = (value: string | null) => {
    if (value && value !== currentOfferId) {
      onSwap(value);
    }
  };

  const swapOptions = useMemo(
    () =>
      offers.map(
        ({ id, vendor, price, clinicPrice, stockStatus, increments }) => ({
          value: id,
          label: vendor.name,
          imageUrl: vendor.imageUrl,
          price: clinicPrice || price,
          stockStatus,
          increments,
        }),
      ),
    [offers],
  );

  const { vendor, stockStatus } =
    offers.find(({ id }) => id === currentOfferId) || {};

  if (!vendor) {
    return null;
  }

  return (
    <DropdownMenu.Root>
      <DropdownMenu.Trigger asChild disabled={!swapOptions.length}>
        <Flex
          mr="auto"
          py="0.3rem"
          pl="0.75rem"
          pr="0.375rem"
          align="center"
          className={mergeClasses(
            'rounded border border-[rgba(34,34,34,0.149)]',
            !swapOptions.length && 'cursor-not-allowed opacity-50',
          )}
        >
          <button
            className={mergeClasses(
              'flex cursor-pointer items-center border-none bg-transparent p-0',
              !swapOptions.length && 'cursor-not-allowed',
            )}
            disabled={!swapOptions.length}
          >
            <img
              src={vendor.imageUrl}
              alt={vendor.name}
              title={vendor.name}
              className="mr-2 h-6 object-contain"
            />
            <div className="mr-3 flex items-center">
              <StockStatusIcon status={stockStatus!} />
            </div>

            {swapOptions.length > 1 ? <ArrowDown /> : null}
          </button>
        </Flex>
      </DropdownMenu.Trigger>
      <DropdownMenu.Portal>
        <DropdownMenu.Content
          className={mergeClasses(
            'z-50 min-w-[120px] rounded-md border border-gray-200 bg-white px-1 py-1 shadow-md',
            'data-[state=open]:animate-in data-[state=closed]:animate-out',
            'data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0',
            'data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95',
            'data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2',
            'data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2',
          )}
          side="bottom"
          align="start"
          sideOffset={2}
        >
          {swapOptions.map(
            ({ label, value, stockStatus, price, imageUrl, increments }) => (
              <DropdownMenu.Item
                key={value}
                className={mergeClasses(
                  'cursor-pointer rounded-sm px-2 py-2 outline-none',
                  'hover:bg-gray-50 focus:bg-gray-50',
                  'data-[highlighted]:bg-gray-50',
                  value === currentOfferId && 'bg-[#f1f3f5]',
                )}
                onClick={() => handleSelectSwapVendor(value)}
              >
                <Flex justify="space-between" align="center" gap="2rem">
                  <Flex align="center" gap="0.5rem">
                    <img
                      src={imageUrl}
                      alt={label}
                      height={24}
                      title={label}
                      className="h-6 w-6 object-contain"
                    />
                    <StockStatusIcon status={stockStatus} />
                  </Flex>
                  <Flex align="flex-end" gap="0.25rem">
                    <span className="text-[10px] font-medium text-[rgba(102,102,102,0.8)]">
                      {increments > 1 ? `Min. incr. ${increments}` : ''}
                    </span>
                    <span className="text-base font-medium">
                      {getPriceString(price)}
                    </span>
                  </Flex>
                </Flex>
              </DropdownMenu.Item>
            ),
          )}
        </DropdownMenu.Content>
      </DropdownMenu.Portal>
    </DropdownMenu.Root>
  );
};
